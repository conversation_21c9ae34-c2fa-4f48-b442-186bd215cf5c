import { Outlet, useLocation } from "react-router-dom";
import { Layout } from "antd";
import { useEffect, useMemo, useState } from "react";

import Header from "@app/layout/StudentLayout/Header";
import Footer from "@app/layout/Footer";
import StudentAside from "@app/layout/StudentLayout/StudentAside";
import "./StudentLayout.scss";
import { LINK } from "@link";
import clsx from "clsx";
import { getChatBotInfo } from "@src/app/services/Settings";
import OwleeChat from "@src/app/component/OwleeChat";

import OWLLEE_ICON from "@src/asset/icon/OwlleeIcon/owllee-icon.svg";
import { ROUTERS } from "@src/constants/routers";

const StudentLayout = () => {

  const location = useLocation();
  const [chatBotId, setChatBotId] = useState(null);

  // useEffect(() => {
  //   getChatBotData();
  // }, []);

  // const getChatBotData = async () => {
  //   const response = await getChatBotInfo();
  //   if (response?.showChatbot && response?.chatbotId) {
  //     setChatBotId(response?.chatbotId);
  //   }
  // }

  const isShowMenu = useMemo(() => {

    const isShowAside = ROUTERS.find(router => {
      const pathRegex = new RegExp(`^${router.path.replace(":id", "[0-9a-fA-F]{24}")}$`);
      return pathRegex.test(location.pathname);
    })?.isShowInAside;
    // console.log(location.pathname, isShowAside);
    return isShowAside || [LINK.COURSES, LINK.ACCOUNT, LINK.EXCHANGE].includes(location.pathname);
  }, [location]);

  const footerStyle = {
    textAlign: "center",
  };

  return (
    <>{
      <Layout className={clsx("student-layout", { "student-layout-has-aside": isShowMenu })}>
        <Header />
        <Layout>
          {isShowMenu && <StudentAside />}
          <Layout.Content className="student-layout__content scrollbar show-scrollbar">
            <Outlet />
            {/* {chatBotId && <OwleeChat chatBotId={chatBotId} icon={OWLLEE_ICON} className="student-owllee" />} */}
          </Layout.Content>
          <Footer style={footerStyle}>Footer</Footer>
        </Layout>
      </Layout>
    }</>
  );
};

export default StudentLayout;
