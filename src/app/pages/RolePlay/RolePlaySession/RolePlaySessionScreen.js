import Loading from '@src/app/component/Loading';
import { getAIPersonaDetails } from '@src/app/services/RolePlay/AIPersonaService';
import { getByCourse, getCourseDetails, getCourseTasks } from '@src/app/services/RolePlay/CourseService';
import { getRolePlaySessionsByStudent } from '@src/app/services/RolePlay/RolePlaySessionService';
import { API } from '@src/constants/api';
import { message } from 'antd';
import { use, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import io from 'socket.io-client';
import { toast } from '@component/ToastProvider';
import './RolePlaySessionScreen.scss';

// Components
import { LINK } from '@src/constants/link';
import CompletedSessionsListComponent from './components/CompletedSessionsListComponent';
import MediaDeviceSelectionModal from './components/MediaDeviceSelectionModal';
import SessionEndModal from './components/SessionEndModal';
import SessionIntroductionComponent from './components/SessionIntroductionComponent';
import SessionMainComponent from './components/SessionMainComponent';
import NewSessionMainScreen from './components/NewSessionMainComponent';
// import TaskSelectionComponent from './components/TaskSelectionComponent'; // Comment out or remove if TaskSelectionComponent is no longer used directly here

// Enum cho trạng thái socket
const SOCKET_STATUS = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  ERROR: 'error',
};

// AudioWorklet processor code as strings
const audioWorkletProcessorCode = `
// AudioWorklet Processor for AI Audio Playback
class AudioChunkProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this.audioChunks = [];
    this.isPlaying = false;
    this.currentFrame = 0;
    this.currentChunkIndex = 0;
    this.currentTurnId = null;
    this.chunkMetadata = []; // Lưu metadata cho từng chunk
    this.frameCounter = 0; // Counter để throttle visualizer updates

    this.port.onmessage = (event) => {
      if (event.data.type === 'addChunk') {
        this.addChunk(event.data.chunk, event.data.turnId, event.data.chunkIndex);
      } else if (event.data.type === 'reset') {
        this.reset();
      } else if (event.data.type === 'resetTurn') {
        this.resetTurn(event.data.turnId);
      }
    };
  }

  addChunk(chunk, turnId, chunkIndex) {
    const chunkData = new Float32Array(chunk);
    this.audioChunks.push(chunkData);
    this.chunkMetadata.push({ turnId, chunkIndex });

    if (!this.isPlaying) {
      this.isPlaying = true;
      this.currentFrame = 0;
      this.currentChunkIndex = 0;
      this.currentTurnId = turnId;
      this.frameCounter = 0;
      this.port.postMessage({
        type: 'playingStarted',
        turnId: turnId,
        chunkIndex: chunkIndex
      });
    }
  }

  reset() {
    this.audioChunks = [];
    this.chunkMetadata = [];
    this.isPlaying = false;
    this.currentFrame = 0;
    this.currentChunkIndex = 0;
    this.currentTurnId = null;
    this.frameCounter = 0;
  }

  resetTurn(turnId) {
    // Xóa tất cả chunks của turn cụ thể
    const indicesToRemove = [];
    for (let i = 0; i < this.chunkMetadata.length; i++) {
      if (this.chunkMetadata[i].turnId === turnId) {
        indicesToRemove.push(i);
      }
    }

    // Xóa từ cuối để không ảnh hưởng index
    for (let i = indicesToRemove.length - 1; i >= 0; i--) {
      const index = indicesToRemove[i];
      this.audioChunks.splice(index, 1);
      this.chunkMetadata.splice(index, 1);
    }

    // Nếu đang phát turn này, dừng lại
    if (this.currentTurnId === turnId) {
      this.isPlaying = false;
      this.currentFrame = 0;
      this.currentChunkIndex = 0;
      this.currentTurnId = null;
      this.frameCounter = 0;
      this.port.postMessage({ type: 'turnInterrupted', turnId: turnId });
    }
  }

  process(inputs, outputs, parameters) {
    const output = outputs[0];
    const outputChannel = output[0]; // Giả sử mono

    if (!this.isPlaying || this.audioChunks.length === 0) {
      // Output silence if not playing or no chunks
      for (let i = 0; i < outputChannel.length; i++) {
        outputChannel[i] = 0;
      }
      return true; // Keep processor alive
    }

    const currentChunk = this.audioChunks[this.currentChunkIndex];
    const currentMetadata = this.chunkMetadata[this.currentChunkIndex];

    if (!currentChunk || !currentMetadata) {
      return true;
    }

    // Tạo buffer nhỏ cho visualizer (128 samples như audioData)
    const visualizerBuffer = new Float32Array(128);
    let visualizerIndex = 0;

    for (let i = 0; i < outputChannel.length; i++) {
      if (this.currentFrame < currentChunk.length) {
        const sample = currentChunk[this.currentFrame];
        outputChannel[i] = sample;

        // Thu thập samples cho visualizer buffer
        if (visualizerIndex < visualizerBuffer.length) {
          visualizerBuffer[visualizerIndex] = sample;
          visualizerIndex++;
        }

        this.currentFrame++;
        this.frameCounter++;
      } else {
        // Chuyển sang chunk tiếp theo
        this.currentChunkIndex++;
        this.currentFrame = 0;

        if (this.currentChunkIndex >= this.audioChunks.length) {
          // Đã phát hết tất cả các chunk
          this.isPlaying = false;
          this.audioChunks = []; // Xóa tất cả các chunk đã phát
          this.chunkMetadata = [];
          this.currentChunkIndex = 0;
          this.frameCounter = 0;
          this.port.postMessage({
            type: 'playingFinished',
            turnId: this.currentTurnId
          });
          this.currentTurnId = null;
          break;
        } else {
          // Tiếp tục với chunk tiếp theo
          const nextChunk = this.audioChunks[this.currentChunkIndex];
          const nextMetadata = this.chunkMetadata[this.currentChunkIndex];

          if (nextChunk && this.currentFrame < nextChunk.length) {
            // Kiểm tra nếu chuyển sang turn mới
            if (nextMetadata.turnId !== this.currentTurnId) {
              this.currentTurnId = nextMetadata.turnId;
              this.port.postMessage({
                type: 'turnChanged',
                newTurnId: this.currentTurnId,
                chunkIndex: nextMetadata.chunkIndex
              });
            }

            const sample = nextChunk[this.currentFrame];
            outputChannel[i] = sample;

            // Thu thập samples cho visualizer buffer
            if (visualizerIndex < visualizerBuffer.length) {
              visualizerBuffer[visualizerIndex] = sample;
              visualizerIndex++;
            }

            this.currentFrame++;
            this.frameCounter++;
          }
        }
      }
    }

    // Gửi dữ liệu real-time cho visualizer với throttling
    // Gửi mỗi 128 frames (tương đương với tần suất audioData)
    if (this.frameCounter >= 128 && visualizerIndex > 0) {
      // Tạo buffer với kích thước thực tế đã thu thập
      const actualBuffer = new Float32Array(visualizerIndex);
      for (let i = 0; i < visualizerIndex; i++) {
        actualBuffer[i] = visualizerBuffer[i];
      }

      this.port.postMessage({
        type: 'playingChunk',
        chunk: actualBuffer,
        frame: this.currentFrame,
        turnId: this.currentTurnId
      });

      this.frameCounter = 0; // Reset counter
    }

    return true;
  }
}

registerProcessor('audio-chunk-processor', AudioChunkProcessor);
`;

const audioCaptureProcessorCode = `
// AudioWorklet Processor for Audio Capture (Microphone)
class AudioCaptureProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super(options);
    // message port để nhận tín hiệu dừng từ main thread (nếu cần)
    this.port.onmessage = (event) => {
      if (event.data === 'stop') {
        // Xử lý dừng nếu cần, ví dụ: dọn dẹp tài nguyên
      }
    };
  }

  process(inputs, outputs, parameters) {
    const inputChannelData = inputs[0]?.[0]; // Kênh mono đầu tiên

    if (inputChannelData && inputChannelData.length > 0) {
      // Tạo bản sao của dữ liệu để có thể chuyển giao (transferable)
      const pcmFloat32 = new Float32Array(inputChannelData);
      // Gửi dữ liệu Float32 PCM về main thread
      // ArrayBuffer được chuyển giao (transferable) để tăng hiệu suất
      this.port.postMessage({ type: 'audioData', pcmData: pcmFloat32.buffer }, [pcmFloat32.buffer]);
    }
    return true; // Luôn trả về true để giữ processor hoạt động
  }
}

registerProcessor('audio-capture-processor', AudioCaptureProcessor);
`;

const AUDIO_CONFIG = {
  sampleRate: 16000,
  channelCount: 1,
  echoCancellation: true,
  noiseSuppression: true,
  autoGainControl: true,
};

export const RolePlaySessionScreen = () => {
  const { t } = useTranslation();
  const { courseId } = useParams();
  const navigate = useNavigate();
  const { user } = useSelector(state => state.auth);
  const location = useLocation();

  // AudioWorklet refs
  const audioContextRef = useRef(null);
  const audioWorkletNodeRef = useRef(null);
  const audioWorkletReadyRef = useRef(false);
  const [audioWorkletError, setAudioWorkletError] = useState(null);

  // Trạng thái
  const [loading, setLoading] = useState(true);
  const [sessionStarted, setSessionStarted] = useState(false);
  const [sessionEnded, setSessionEnded] = useState(false);
  const [recording, setRecording] = useState(false);
  const [showIntroduction, setShowIntroduction] = useState(true);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [transcript, setTranscript] = useState([]);
  const [paused, setPaused] = useState(false);
  const [passScore, setPassScore] = useState(80);
  const [tasksList, setTasksList] = useState([]);
  const [showTaskSelection, setShowTaskSelection] = useState(false); // Initial state changed, will be managed differently or removed

  // State for completed sessions
  const [completedSessions, setCompletedSessions] = useState([]);
  const [loadingCompletedSessions, setLoadingCompletedSessions] = useState(true);

  // Media Devices State
  const [showMediaDeviceModal, setShowMediaDeviceModal] = useState(false);
  const [availableCameras, setAvailableCameras] = useState([]);
  const [availableMics, setAvailableMics] = useState([]);
  const [selectedCamera, setSelectedCamera] = useState(null);
  const [selectedMic, setSelectedMic] = useState(null);
  const [availableSpeakers, setAvailableSpeakers] = useState([]);
  const [selectedSpeaker, setSelectedSpeaker] = useState(null);
  const [isSpeakerSelectionSupported, setIsSpeakerSelectionSupported] = useState(false);
  const [cameraEnabled, setCameraEnabled] = useState(true);
  const [micEnabled, setMicEnabled] = useState(true); // Thêm state micEnabled
  const [enumeratingDevices, setEnumeratingDevices] = useState(false);

  // Dữ liệu
  const [course, setCourse] = useState(null);
  const [persona, setPersona] = useState(null);
  const [sessionId, setSessionId] = useState(null);
  const [scenarios, setScenarios] = useState([]);
  const [scenariosDetail, setScenariosDetail] = useState(null);

  // Refs
  const videoRef = useRef(null);
  const timerRef = useRef(null);
  const socketRef = useRef(null);

  // Refs cho AudioWorklet thu âm
  const captureAudioContextRef = useRef(null);
  const captureAudioWorkletNodeRef = useRef(null);
  const mediaStreamSourceRef = useRef(null);
  const audioCaptureWorkletReadyRef = useRef(false);
  const recordingRef = useRef(false);

  // Thêm state để hiển thị lỗi media
  const [mediaError, setMediaError] = useState(null);
  const currentStreamRef = useRef(null);

  const [socketStatus, setSocketStatus] = useState(SOCKET_STATUS.DISCONNECTED);
  const aiAudioPlayerRef = useRef(new Audio());
  const [isAiSpeaking, setIsAiSpeaking] = useState(false);
  const [isAiProcessing, setIsAiProcessing] = useState(false);
  const [isAiInterrupted, setIsAiInterrupted] = useState(false);
  const audioQueueRef = useRef([]);
  const [currentAudioFrameForVisualizer, setCurrentAudioFrameForVisualizer] = useState(null);
  const [aiAudioFrameForVisualizer, setAiAudioFrameForVisualizer] = useState(null); // State for AI visualizer data
  const currentAiTurnIdRef = useRef(null); // Theo dõi turnId hiện tại của AI

  // Thêm state cho Rate Control và Adaptive Streaming
  const audioChunksBufferRef = useRef(new Map()); // Buffer cho chunks theo turnId
  const expectedChunkIndexRef = useRef(new Map()); // Theo dõi chunk index mong đợi cho mỗi turn

  // API Functions
  const fetchCourseAndTaskData = async () => {
    setLoading(true);
    try {
      // Lấy thông tin khóa học
      const courseResponse = await getCourseDetails(courseId, ['references']);
      if (courseResponse) {
        setCourse(courseResponse);
        // Nếu API course trả về object persona thì dùng luôn, nếu không thì gọi API riêng
        // if (courseResponse.persona) {
        //   setPersona(courseResponse.persona);
        // } else if (courseResponse.aiPersonaId) {
        //   const personaDetails = await getAIPersonaDetails(courseResponse.aiPersonaId);
        //   setPersona(personaDetails);
        // } else {
        //   message.warning(t('NO_PERSONA_ASSIGNED', 'Khóa học này chưa được gán AI Persona.'));
        //   // Có thể set persona mặc định hoặc không cho bắt đầu session
        // }
        // Set passScore from course if available
        if (courseResponse.passScore) {
          setPassScore(courseResponse.passScore);
        }
      } else {
        toast.error(t('ERROR_FETCHING_COURSE', 'Lỗi khi tải thông tin khóa học'));
        navigate(-1);
        return;
      }

      // Lấy danh sách tất cả các task của khóa học
      const tasksListResponse = await getCourseTasks(courseId);
      console.log('tasksListResponse', tasksListResponse);
      if (tasksListResponse && tasksListResponse.tasks && tasksListResponse.tasks?.rows?.length > 0) {
        setTasksList(tasksListResponse.tasks.rows);
      } else {
        // It's okay if a course has no tasks, or tasks are not used for this new flow directly for starting.
        // message.error(t('NO_TASKS_FOUND_FOR_COURSE', 'Không tìm thấy nhiệm vụ nào cho khóa học này.'));
        console.warn(
          t('NO_TASKS_FOUND_FOR_COURSE', 'Không tìm thấy nhiệm vụ nào cho khóa học này hoặc chúng không bắt buộc.'),
        );
        setTasksList([]); // Ensure tasksList is an empty array
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error(t('ERROR_FETCHING_DATA', 'Đã xảy ra lỗi khi tải dữ liệu'));
      navigate(-1);
    } finally {
      setLoading(false);
    }
  };

  const fetchGetByCourse = async () => {
    setLoading(true);
    try {
      const courseResponse = await getByCourse(courseId);
      if (courseResponse) {
        setScenarios(courseResponse);
      }
      else {
        message.error(t('ERROR_FETCHING_COURSE', 'Lỗi khi tải thông tin khóa học'));
        navigate(-1);
        return;
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      message.error(t('ERROR_FETCHING_DATA', 'Đã xảy ra lỗi khi tải dữ liệu'));
      navigate(-1);
    } finally {
      setLoading(false);
    }
  }

  // Quay lại danh sách task
  const handleLeaveSession = () => {
    // Renamed from backToTaskList
    setShowTaskSelection(false); // Ensure we don't show task selection area
    // Reset lại các state liên quan đến phiên
    setSessionStarted(false);
    setSessionEnded(false);
    setShowIntroduction(true);
    setElapsedTime(0);
    setTranscript([]);
    setPaused(false);
    setSessionId(null); // Reset session ID

    // Reset các trạng thái AI
    setIsAiSpeaking(false);
    setIsAiProcessing(false);
    setIsAiInterrupted(false);
    currentAiTurnIdRef.current = null;

    // Cleanup media
    cleanupMediaResources(); // Dọn dẹp tài nguyên ghi âm và stream
    cleanupAudioWorklet(); // Dọn dẹp tài nguyên AudioWorklet playback

    // Dừng timer nếu đang chạy
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // Ngắt kết nối WebSocket nếu có
    if (socketRef.current) {
      console.log('[handleLeaveSession] Disconnecting WebSocket');
      socketRef.current.disconnect();
      socketRef.current = null;
      setSocketStatus(SOCKET_STATUS.DISCONNECTED);
    }

    // Reset lựa chọn thiết bị media
    setSelectedCamera(null);
    setSelectedMic(null);
    setAvailableCameras([]);
    setAvailableMics([]);
    // Không cần reset audioWorkletReadyRef và audioCaptureWorkletReadyRef ở đây
    // vì chúng được xử lý trong cleanupAudioWorklet và cleanupMediaResources tương ứng

    // Navigate back to the previous screen (e.g., course list)
    // navigate('/student/courses');
  };

  // Hàm khởi tạo AudioWorklet
  const initAudioWorklet = async () => {
    try {
      // Kiểm tra hỗ trợ AudioWorklet
      if (!window.AudioContext || !('audioWorklet' in new AudioContext())) {
        console.warn(
          '[initAudioWorklet] Trình duyệt không hỗ trợ AudioWorklet, sẽ sử dụng phương pháp phát âm thanh thông thường',
        );
        setAudioWorkletError('Trình duyệt không hỗ trợ AudioWorklet');
        return false;
      }

      // Tạo AudioContext mới nếu chưa có
      if (!audioContextRef.current) {
        audioContextRef.current = new AudioContext();
        console.log('[initAudioWorklet] Đã tạo AudioContext mới');

        // Set sink ID right after creation if a speaker is already selected
        if (selectedSpeaker && isSpeakerSelectionSupported) {
          audioContextRef.current.setSinkId(selectedSpeaker).catch(err => {
            console.error(
              `[initAudioWorklet] Initial setSinkId failed for ${selectedSpeaker}: ${err.name} - ${err.message}`,
            );
          });
        }
      }

      // Kiểm tra trạng thái AudioContext
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
        console.log('[initAudioWorklet] Đã resume AudioContext');
      }

      // Tạo Blob URL từ code AudioWorkletProcessor
      const blob = new Blob([audioWorkletProcessorCode], { type: 'application/javascript' });
      const processorUrl = URL.createObjectURL(blob);

      try {
        // Đăng ký AudioWorkletProcessor
        await audioContextRef.current.audioWorklet.addModule(processorUrl);
        console.log('[initAudioWorklet] Đã đăng ký AudioWorkletProcessor');

        // Tạo AudioWorkletNode
        audioWorkletNodeRef.current = new AudioWorkletNode(audioContextRef.current, 'audio-chunk-processor');
        audioWorkletNodeRef.current.connect(audioContextRef.current.destination);
        console.log('[initAudioWorklet] Đã tạo và kết nối AudioWorkletNode');

        // Thiết lập message handler với cải thiện hiệu suất
        audioWorkletNodeRef.current.port.onmessage = event => {
          if (event.data.type === 'playingStarted') {
            console.log('[AudioWorklet] Bắt đầu phát âm thanh cho turn:', event.data.turnId);
            if (!isAiInterrupted) {
              setIsAiSpeaking(true);
            }
          } else if (event.data.type === 'playingFinished') {
            console.log('[AudioWorklet] Kết thúc phát âm thanh cho turn:', event.data.turnId);
            setIsAiSpeaking(false);
            setAiAudioFrameForVisualizer(null); // Xóa dữ liệu visualizer của AI khi nói xong

            // Cleanup buffer cho turn đã hoàn thành
            if (event.data.turnId) {
              audioChunksBufferRef.current.delete(event.data.turnId);
              expectedChunkIndexRef.current.delete(event.data.turnId);
            }

            if (!isAiInterrupted) {
              processAudioQueue(); // Xử lý chunk tiếp theo nếu có
            }
          } else if (event.data.type === 'turnChanged') {
            console.log('[AudioWorklet] Chuyển sang turn mới:', event.data.newTurnId);
            currentAiTurnIdRef.current = event.data.newTurnId;
          } else if (event.data.type === 'turnInterrupted') {
            console.log('[AudioWorklet] Turn bị ngắt lời:', event.data.turnId);
            setIsAiSpeaking(false);
            setAiAudioFrameForVisualizer(null);

            // Cleanup buffer cho turn bị ngắt lời
            if (event.data.turnId) {
              audioChunksBufferRef.current.delete(event.data.turnId);
              expectedChunkIndexRef.current.delete(event.data.turnId);
            }
          } else if (event.data.type === 'playingChunk') {
            // Cải thiện: Nhận dữ liệu real-time với kích thước nhỏ hơn
            setAiAudioFrameForVisualizer(event.data.chunk);
          }
        };

        // Đánh dấu AudioWorklet đã sẵn sàng
        audioWorkletReadyRef.current = true;
        console.log('[initAudioWorklet] AudioWorklet đã sẵn sàng');
        return true;
      } finally {
        // Giải phóng Blob URL
        URL.revokeObjectURL(processorUrl);
      }
    } catch (error) {
      console.error('[initAudioWorklet] Lỗi khi khởi tạo AudioWorklet:', error);
      setAudioWorkletError(`Lỗi khi khởi tạo AudioWorklet: ${error.message}`);
      return false;
    }
  };

  // Hàm dừng AI ngay lập tức khi bị ngắt lời
  const stopAiImmediately = (turnId = null) => {
    console.log('[stopAiImmediately] Stopping AI immediately due to interruption', { turnId });

    // Xóa hàng đợi audio
    audioQueueRef.current = [];

    // Xóa buffer chunks cho turn hiện tại hoặc tất cả
    if (turnId) {
      audioChunksBufferRef.current.delete(turnId);
      expectedChunkIndexRef.current.delete(turnId);
    } else {
      audioChunksBufferRef.current.clear();
      expectedChunkIndexRef.current.clear();
    }

    // Reset AudioWorklet để dừng phát âm thanh ngay lập tức
    if (audioWorkletNodeRef.current && audioWorkletReadyRef.current) {
      if (turnId) {
        // Reset chỉ turn cụ thể
        audioWorkletNodeRef.current.port.postMessage({ type: 'resetTurn', turnId: turnId });
      } else {
        // Reset tất cả
        audioWorkletNodeRef.current.port.postMessage({ type: 'reset' });
      }
    }

    // Cập nhật trạng thái
    setIsAiSpeaking(false);
    setIsAiProcessing(false);
    setAiAudioFrameForVisualizer(null);

    console.log('[stopAiImmediately] AI stopped immediately');
  };

  // Hàm xử lý adaptive audio chunks với rate control
  const processAdaptiveAudioChunk = async chunkData => {
    const { audioChunk, chunkIndex, offset, totalSize, isLast, turnId } = chunkData;

    console.log(
      `[processAdaptiveAudioChunk] Processing chunk ${chunkIndex} for turn ${turnId}, offset: ${offset}/${totalSize}`,
    );

    // Kiểm tra nếu AI bị ngắt lời
    if (isAiInterrupted) {
      console.log(`[processAdaptiveAudioChunk] AI interrupted, ignoring chunk ${chunkIndex}`);
      return;
    }

    // Cập nhật progress

    // Khởi tạo buffer cho turn này nếu chưa có
    if (!audioChunksBufferRef.current.has(turnId)) {
      audioChunksBufferRef.current.set(turnId, []);
      expectedChunkIndexRef.current.set(turnId, 0);
    }

    const expectedIndex = expectedChunkIndexRef.current.get(turnId);

    // Kiểm tra thứ tự chunk
    if (chunkIndex !== expectedIndex) {
      console.warn(`[processAdaptiveAudioChunk] Out of order chunk. Expected: ${expectedIndex}, Got: ${chunkIndex}`);
      // Có thể implement buffer để xử lý out-of-order chunks nếu cần
    }

    // Thêm chunk vào buffer
    const turnBuffer = audioChunksBufferRef.current.get(turnId);
    turnBuffer.push(audioChunk);
    expectedChunkIndexRef.current.set(turnId, chunkIndex + 1);

    // Xử lý chunk ngay lập tức với AudioWorklet
    try {
      // Kiểm tra và khởi tạo AudioWorklet nếu cần
      if (!audioWorkletReadyRef.current) {
        const initialized = await initAudioWorklet();
        if (!initialized) {
          console.log('[processAdaptiveAudioChunk] AudioWorklet not available, using fallback');
          return;
        }
      }

      // Chuyển đổi dữ liệu âm thanh
      let audioData = audioChunk;
      if (audioChunk instanceof Blob) {
        audioData = await audioChunk.arrayBuffer();
      }

      if (!audioData || audioData.byteLength === 0) {
        console.warn('[processAdaptiveAudioChunk] Empty audio data, skipping chunk');
        return;
      }

      // Giải mã audio data thành PCM
      const audioContext = audioContextRef.current;
      const audioBuffer = await audioContext.decodeAudioData(audioData);
      const channelData = audioBuffer.getChannelData(0);

      // Gửi chunk đến AudioWorklet
      audioWorkletNodeRef.current.port.postMessage({
        type: 'addChunk',
        chunk: channelData,
        turnId: turnId,
        chunkIndex: chunkIndex,
      });

      console.log(`[processAdaptiveAudioChunk] Successfully processed chunk ${chunkIndex} for turn ${turnId}`);
    } catch (error) {
      console.error(`[processAdaptiveAudioChunk] Error processing chunk ${chunkIndex}:`, error);
    }

    // Nếu là chunk cuối cùng, cleanup buffer
    if (isLast) {
      console.log(`[processAdaptiveAudioChunk] Last chunk processed for turn ${turnId}`);
    }
  };

  // Hàm xử lý audio chunks từ AI sử dụng AudioWorklet (legacy)
  const processAudioQueueWithWorklet = async () => {
    if (isAiSpeaking || audioQueueRef.current.length === 0 || isAiInterrupted) {
      return;
    }

    // Kiểm tra và khởi tạo AudioWorklet nếu cần
    if (!audioWorkletReadyRef.current) {
      const initialized = await initAudioWorklet();
      if (!initialized) {
        // Nếu không thể khởi tạo AudioWorklet, sử dụng phương pháp phát âm thanh thông thường
        console.log(
          '[processAudioQueueWithWorklet] Không thể khởi tạo AudioWorklet, chuyển sang phương pháp thông thường',
        );
        processAudioQueue();
        return;
      }
    }

    // Lấy chunk âm thanh từ hàng đợi
    const audioChunk = audioQueueRef.current.shift();
    console.log(
      '[processAudioQueueWithWorklet] Đã nhận audio chunk, kích thước:',
      audioChunk ? audioChunk.byteLength || audioChunk.size || 'unknown' : 'null',
    );

    try {
      // Chuyển đổi dữ liệu âm thanh thành ArrayBuffer nếu cần
      let audioData = audioChunk;
      if (audioChunk instanceof Blob) {
        audioData = await audioChunk.arrayBuffer();
      }
      if (!audioData || audioData.byteLength === 0) {
        console.warn(
          '[processAudioQueueWithWorklet] Received empty or invalid audio data after potential conversion. Skipping.',
        );
        setIsAiSpeaking(false); // Ensure speaking state is reset if queue becomes empty due to bad data
        setAiAudioFrameForVisualizer(null);
        processAudioQueueWithWorklet(); // Try next chunk
        return;
      }

      // Giải mã audio data thành PCM
      const audioContext = audioContextRef.current;
      const audioBuffer = await audioContext.decodeAudioData(audioData);

      // Lấy dữ liệu kênh đầu tiên (mono)
      const channelData = audioBuffer.getChannelData(0);

      // Cập nhật state cho visualizer của AI
      setAiAudioFrameForVisualizer(channelData);

      // Gửi dữ liệu âm thanh đến AudioWorkletProcessor
      audioWorkletNodeRef.current.port.postMessage({
        type: 'addChunk',
        chunk: channelData,
      });

      console.log('[processAudioQueueWithWorklet] Đã gửi audio chunk đến AudioWorkletProcessor');
    } catch (error) {
      console.error('[processAudioQueueWithWorklet] Lỗi khi xử lý audio chunk:', error);
      setIsAiSpeaking(false);
      setAiAudioFrameForVisualizer(null); // Xóa visualizer của AI khi có lỗi
      // Thử xử lý chunk tiếp theo
      processAudioQueueWithWorklet();
    }
  };

  // Thay thế hàm processAudioQueue cũ
  const processAudioQueue = () => {
    // Kiểm tra nếu AI bị ngắt lời
    if (isAiInterrupted) {
      return;
    }

    // Thử sử dụng AudioWorklet nếu không có lỗi
    if (!audioWorkletError) {
      processAudioQueueWithWorklet();
      return;
    }

    // Fallback to existing implementation if AudioWorklet is not available
    if (isAiSpeaking || audioQueueRef.current.length === 0) {
      return;
    }

    // Existing implementation...
    // ... rest of the existing processAudioQueue function ...
  };

  // Cleanup AudioWorklet khi component unmount
  const cleanupAudioWorklet = () => {
    if (audioWorkletNodeRef.current) {
      audioWorkletNodeRef.current.disconnect();
      audioWorkletNodeRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close().catch(err => {
        console.error('[cleanupAudioWorklet] Lỗi khi đóng AudioContext:', err);
      });
      audioContextRef.current = null;
    }

    audioWorkletReadyRef.current = false;
  };

  // Lấy danh sách camera và mic
  const enumerateDevices = async () => {
    setEnumeratingDevices(true);
    try {
      console.log('Bắt đầu liệt kê thiết bị media...');

      // Kiểm tra quyền truy cập trước khi gọi getUserMedia
      const hasCameraPermission = await navigator.permissions.query({ name: 'camera' });
      const hasMicrophonePermission = await navigator.permissions.query({ name: 'microphone' });

      if (hasCameraPermission.state === 'denied') {
        console.warn('Quyền truy cập camera bị từ chối trước.');
        message.info(t('NO_CAMERAS_FOUND_INFO_WARN', 'Không tìm thấy camera. Bạn có thể tiếp tục mà không có hình ảnh.'));
        setAvailableCameras([]); // Không hiển thị camera nếu không có quyền
      }

      if (hasMicrophonePermission.state === 'denied') {
        console.warn('Quyền truy cập microphone bị từ chối trước.');
        toast.error(t('NO_MICROPHONES_FOUND_WARN_INFO', 'Không tìm thấy microphone. Bạn sẽ không thể bắt đầu phiên hội thoại.'));
        setAvailableMics([]); // Không hiển thị camera nếu không có quyền
      }


      // Thực hiện việc liệt kê hai lần
      // Lần đầu tiên để lấy danh sách thiết bị (có thể không có label)
      let initialDevices = await navigator.mediaDevices.enumerateDevices();
      console.log('Danh sách thiết bị ban đầu:', initialDevices);

      let hasMicPermission = false;
      // Yêu cầu quyền truy cập để đảm bảo các device sẽ có label.
      // Bắt đầu với audio vì nó là bắt buộc và cần thiết để lấy label cho loa.
      try {
        const audioStream = await navigator.mediaDevices.getUserMedia({ audio: AUDIO_CONFIG });
        console.log('Đã nhận được quyền truy cập microphone.');
        hasMicPermission = true;
        // Dừng stream audio ngay sau khi có quyền để không giữ tài nguyên.
        audioStream.getTracks().forEach(track => {
          console.log(`Dừng track audio tạm thời: ${track.kind} - ${track.label}`);
          track.stop();
        });
      } catch (audioErr) {
        console.warn('Không thể truy cập microphone (người dùng có thể đã từ chối):', audioErr);
        // toast.error(t('MICROPHONE_ACCESS_REQUIRED', 'Cần có quyền truy cập microphone để bắt đầu phiên.'));
      }

      // Sau khi có quyền audio, thử lấy quyền video nếu camera được bật.
      // Điều này tách biệt hai yêu cầu, cho phép người dùng từ chối camera mà không ảnh hưởng đến mic/loa.
      if (cameraEnabled) {
        try {
          const videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
          console.log('Đã nhận được quyền truy cập camera.');
          // Dừng stream video ngay.
          videoStream.getTracks().forEach(track => {
            console.log(`Dừng track video tạm thời: ${track.kind} - ${track.label}`);
            track.stop();
          });
        } catch (videoErr) {
          console.warn('Không thể truy cập camera (người dùng có thể đã từ chối).', videoErr);
          // Không ném lỗi ở đây, vì camera là tùy chọn. Vẫn hiển thị thông báo nếu cần.
          // Người dùng sẽ thấy thông báo không có camera.
        }
      }

      // Sau khi có quyền, liệt kê lại để lấy đầy đủ thông tin bao gồm label
      const devices = await navigator.mediaDevices.enumerateDevices();
      console.log('Danh sách thiết bị sau khi có quyền:', devices);

      // So sánh với danh sách ban đầu để xem có thiết bị nào bị thiếu không
      const initialDeviceIds = new Set(initialDevices.map(d => d.deviceId));
      const missingDevices = devices.filter(d => !initialDeviceIds.has(d.deviceId));
      if (missingDevices.length > 0) {
        console.log('Phát hiện thiết bị mới sau khi cấp quyền:', missingDevices);
      }

      // Lọc và log chi tiết
      const cameras = devices.filter(device => device.kind === 'videoinput' && !!device.deviceId);
      // Chỉ hiển thị mic và loa nếu có quyền
      const mics = hasMicPermission ? devices.filter(device => device.kind === 'audioinput' && !!device.deviceId) : [];
      const speakers = hasMicPermission ? devices.filter(device => device.kind === 'audiooutput' && !!device.deviceId) : [];
      console.log(`Tìm thấy ${cameras.length} camera:`, cameras);
      console.log(`Tìm thấy ${mics.length} microphone:`, mics);
      console.log(`Tìm thấy ${speakers.length} loa:`, speakers);

      // Hiển thị tất cả thiết bị, ngay cả khi label là rỗng
      setAvailableCameras(cameras);
      setAvailableMics(mics);
      setAvailableSpeakers(speakers);
      
      // Chọn thiết bị mặc định đầu tiên nếu có
      // Ưu tiên thiết bị có label trước
      const cameraWithLabel = cameras.find(c => c.label);
      const micWithLabel = mics.find(m => m.label);

      if (cameraWithLabel || cameras.length > 0) {
        const defaultCamera = cameraWithLabel || cameras[0];
        setSelectedCamera(defaultCamera.deviceId);
        console.log('Camera mặc định đã được chọn:', defaultCamera.label || defaultCamera.deviceId);
      }

      if (micWithLabel || mics.length > 0) {
        const defaultMic = micWithLabel || mics[0];
        setSelectedMic(defaultMic.deviceId);
        console.log('Microphone mặc định đã được chọn:', defaultMic.label || defaultMic.deviceId);
      } else {
        console.warn('Không tìm thấy microphone nào. Vui lòng kiểm tra quyền truy cập.');
      }

      if (speakers.length > 0) {
        const defaultSpeaker = speakers.find(s => s.deviceId === 'default') || speakers[0];
        setSelectedSpeaker(defaultSpeaker.deviceId);
        console.log('Loa mặc định đã được chọn:', defaultSpeaker.label || defaultSpeaker.deviceId);
      }
    } catch (err) {
      console.error('Lỗi không mong muốn khi liệt kê thiết bị:', err);
      toast.error(`${t('UNEXPECTED_DEVICE_ERROR', 'Lỗi không mong muốn khi liệt kê thiết bị')}: ${err.message}`);
    } finally {
      setEnumeratingDevices(false);
    }
  };

  // Bắt đầu phiên role play - được sửa đổi để hiển thị modal chọn thiết bị
  const startSession = () => {
    setShowIntroduction(false);

    // Mở modal chọn thiết bị trước khi bắt đầu session
    enumerateDevices();
    setShowMediaDeviceModal(true);
  };

  // Hàm bắt đầu session thực sự sau khi chọn thiết bị
  const startSessionWithSelectedDevices = () => {
    setShowMediaDeviceModal(false);

    // Không setSessionStarted(true) ngay lập tức
    // Nó sẽ được set khi server xác nhận 'server:session_started'
    // Khởi tạo WebSocket connection
    if (!socketRef.current && course && persona) {
      // Ensure course and persona are available
      console.log('Connecting to WebSocket:');
      socketRef.current = io(API.ROLE_PLAY_WEBSOCKET, { transports: ['websocket'], path: '/socket' });
      setSocketStatus(SOCKET_STATUS.CONNECTING);

      socketRef.current.on('connect', () => {
        console.log('WebSocket connected');
      });

      socketRef.current.on('server:ready', () => {
        setSocketStatus(SOCKET_STATUS.CONNECTED);
        // Gửi sự kiện bắt đầu phiên sau khi kết nối thành công
        socketRef.current.emit('client:start_session', {
          courseId: course._id,
          // taskId: task._id, // Removed: No longer sending taskId
          aiPersonaId: persona._id,
          studentId: user._id,
        });
      });

      socketRef.current.on('disconnect', reason => {
        console.log('WebSocket disconnected:', reason);
        setSocketStatus(SOCKET_STATUS.DISCONNECTED);
        // Có thể thêm logic retry hoặc thông báo cho người dùng
        if (sessionStarted && !sessionEnded) {
          toast.error(t('WEBSOCKET_DISCONNECTED', 'Mất kết nối tới server. Vui lòng thử lại.'));
          // Có thể cân nhắc tự động gọi endSession hoặc backToTaskList
        }
      });

      socketRef.current.on('connect_error', error => {
        console.error('WebSocket connection error:', error);
        setSocketStatus(SOCKET_STATUS.ERROR);
        toast.error(t('WEBSOCKET_CONNECTION_ERROR', 'Không thể kết nối tới server.'));
        // Reset lại để người dùng có thể thử lại
        setShowIntroduction(true);
        setSessionStarted(false);
        if (socketRef.current) {
          socketRef.current.disconnect();
          socketRef.current = null;
        }
      });

      socketRef.current.on('server:session_started', data => {
        console.log('Server acknowledged session start:', data);
        if (data._id) {
          setSessionId(data._id);
          setSessionStarted(true); // Chính thức bắt đầu phiên
          setElapsedTime(totalDurationSeconds); // Reset timer
          // setTranscript([]); // Reset transcript
          // Bắt đầu timer
          if (timerRef.current) clearInterval(timerRef.current);
          timerRef.current = setInterval(() => {
            if (!paused) {
              setElapsedTime(prevTime => {
                const newTime = prevTime - 1;
                // Kiểm tra nếu thời gian đã vượt quá totalDurationSeconds
                // if (totalDurationSeconds > 0 && newTime >= totalDurationSeconds) {
                if (totalDurationSeconds > 0 && newTime <= 0) {
                  // Tự động kết thúc session khi vượt quá thời gian
                  console.log('Session time limit reached. Auto ending session.');
                  clearInterval(timerRef.current);
                  endSession();
                }
                return newTime;
              });
            }
          }, 1000);
          // Bắt đầu ghi âm sau khi session được server xác nhận
          // startRecording will now be called from setupMediaDevices upon successful setup
        } else {
          console.error('[RolePlaySessionScreen] Session ID not received from server');
          toast.error(t('SESSION_START_FAILED_NO_ID', 'Không thể bắt đầu phiên, không nhận được ID.'));
          // Xử lý lỗi, có thể quay lại màn hình chọn task
          handleLeaveSession(); // This should be handleLeaveSession or similar to navigate back
        }
      });

      socketRef.current.on('server:ai_speech_chunk', data => {
        console.log('Received AI speech chunk with metadata:', data);

        // Kiểm tra nếu AI bị ngắt lời, không xử lý chunk mới
        if (isAiInterrupted) {
          console.log('[server:ai_speech_chunk] AI interrupted, ignoring chunk');
          return;
        }

        // Kiểm tra nếu có metadata mới (adaptive streaming)
        if (data.chunkIndex !== undefined && data.turnId && data.totalSize !== undefined) {
          // Xử lý adaptive streaming chunk
          console.log(
            `[server:ai_speech_chunk] Processing adaptive chunk ${data.chunkIndex}/${Math.ceil(
              data.totalSize / 4096,
            )} for turn ${data.turnId}`,
          );

          if (data.audioChunk) {
            // Xử lý dữ liệu âm thanh từ server
            let audioData = data.audioChunk;
            // Xử lý adaptive chunk
            processAdaptiveAudioChunk({
              audioChunk: audioData,
              chunkIndex: data.chunkIndex,
              offset: data.offset,
              totalSize: data.totalSize,
              isLast: data.isLast,
              turnId: data.turnId,
            });
          }
        }

        if (data.textChunk) {
          setTranscript(prev => [
            ...prev,
            { speaker: 'ai', text: data.textChunk, timestamp: new Date().toISOString(), type: 'interim_ai' },
          ]);
        }
      });

      socketRef.current.on('server:ai_text_response', data => {
        console.log('Received AI sentence for turn:', data);

        // Kiểm tra nếu AI bị ngắt lời, không xử lý text response mới
        if (isAiInterrupted) {
          console.log('[server:ai_text_response] AI interrupted, ignoring text response');
          return;
        }

        if (data.text && data.role === 'assistant' && data.turnId) {
          // Kiểm tra nếu đây là turn mới của AI, reset trạng thái ngắt lời
          if (currentAiTurnIdRef.current !== data.turnId) {
            currentAiTurnIdRef.current = data.turnId;
            setIsAiInterrupted(false);
            setIsAiProcessing(false);
          }

          setTranscript(prev => {
            // 1. Lọc ra tất cả các tin nhắn của người dùng.
            const userMessages = prev.filter(msg => msg.speaker === 'user');

            // 2. Tìm tin nhắn AI trước đó cho cùng turnId (nếu có) để lấy text cũ.
            const previousAiMessageForThisTurn = prev.find(msg => msg.speaker === 'ai' && msg.turnId === data.turnId);

            let currentAiText;
            if (previousAiMessageForThisTurn) {
              // Nếu có, nối câu mới vào text của turn hiện tại.
              currentAiText = previousAiMessageForThisTurn.text + ' ' + data.text;
            } else {
              // Nếu không (turn mới hoặc câu đầu tiên của turn), text mới là nội dung.
              currentAiText = data.text;
            }

            // 3. Tạo đối tượng tin nhắn AI cho turn hiện tại.
            const currentAiTurnMessage = {
              speaker: 'ai',
              text: currentAiText,
              turnId: data.turnId,
              role: data.role, // Lưu lại role từ server
              timestamp: new Date().toISOString(),
              // Không cần 'type' ở đây nữa nếu logic này là chủ đạo cho text AI
            };

            // 4. Trả về transcript mới: user messages + tin nhắn AI của turn hiện tại.
            // Điều này đảm bảo chỉ có một tin nhắn AI (của turn hiện tại) được hiển thị và cập nhật.
            return [...userMessages, currentAiTurnMessage];
          });
        }
      });

      socketRef.current.on('server:user_transcript', data => {
        console.log('Received user transcript from server:', data);
        if (data.text) {
          // Khi user nói, reset trạng thái ngắt lời để chuẩn bị cho lượt AI tiếp theo
          setIsAiInterrupted(false);

          // Cập nhật hoặc thêm vào transcript của user, tùy theo logic STT của server
          // Nếu server gửi transcript đầy đủ sau khi user nói xong:
          setTranscript(prev => {
            // Loại bỏ các tin nhắn "đang nói" tạm thời của user nếu có (ví dụ: type 'interim_user')
            const filtered = prev.filter(msg => !(msg.speaker === 'user' && msg.type === 'interim_user'));
            return [
              ...filtered,
              { speaker: 'user', text: data.text, timestamp: new Date().toISOString(), type: 'final_user' },
            ];
          });
        }
      });

      // Xử lý sự kiện user bắt đầu nói (có thể ngắt lời AI)
      socketRef.current.on('server:student_text_response', data => {
        console.log('Received student text response:', data);
        if (data.text) {
          if (data.isFinal) {
            // Transcript cuối cùng của user
            setTranscript(prev => {
              const filtered = prev.filter(msg => !(msg.speaker === 'user' && msg.type === 'interim_user'));
              return [
                ...filtered,
                {
                  speaker: 'user',
                  text: data.text,
                  timestamp: new Date().toISOString(),
                  type: 'final_user',
                  turnAudioId: data.turnAudioId,
                },
              ];
            });
          } else {
            // Transcript tạm thời của user (interim)
            setTranscript(prev => {
              const filtered = prev.filter(msg => !(msg.speaker === 'user' && msg.type === 'interim_user'));
              const aiMessages = prev.filter(msg => msg.speaker === 'ai');
              return [
                ...aiMessages,
                {
                  speaker: 'user',
                  text: data.text,
                  timestamp: new Date().toISOString(),
                  type: 'interim_user',
                },
              ];
            });
          }
        }
      });

      // Xử lý sự kiện AI bắt đầu TTS
      socketRef.current.on('server:ai_tts_started', data => {
        console.log('AI TTS started:', data);
        setIsAiProcessing(false);
        // setIsAiSpeaking(true);
      });

      // Xử lý sự kiện AI hoàn thành TTS
      socketRef.current.on('server:ai_tts_completed', data => {
        console.log('AI TTS completed:', data);
        // setIsAiSpeaking(false);
      });

      // Xử lý sự kiện AI bị ngắt lời (legacy)
      socketRef.current.on('server:ai_interrupted', data => {
        console.log('AI interrupted by user (legacy):', data);
        setIsAiInterrupted(true);
        stopAiImmediately();

        // Chỉ console.log, không hiển thị message trên UI và không cập nhật transcript
        console.log('[server:ai_interrupted] AI was interrupted. Message from server:', data.message);
      });

      // Xử lý sự kiện AI speech bị ngắt lời (enhanced)
      socketRef.current.on('server:ai_speech_interrupted', data => {
        console.log('AI speech interrupted with details:', data);
        const { sessionId, turnId, bytesSent, totalBytes } = data;

        setIsAiInterrupted(true);
        stopAiImmediately(turnId);
      });

      // Xử lý sự kiện AI bắt đầu xử lý (thinking)
      socketRef.current.on('server:ai_processing_started', data => {
        console.log('AI processing started:', data);
        setIsAiProcessing(true);
        setIsAiInterrupted(false);
      });

      socketRef.current.on('server:session_ended_confirmed', () => {
        console.log('Server confirmed session end');
        // Các xử lý khác nếu cần sau khi server xác nhận kết thúc
      });

      socketRef.current.on('server:error', errorData => {
        console.error('Server error via WebSocket:', errorData);
        toast.error(`${t('SERVER_ERROR', 'Lỗi từ server')}: ${errorData.message || 'Unknown error'}`);
        // Có thể thêm xử lý cụ thể tùy theo mã lỗi
        if (errorData.action === 'force_end_session') {
          setSessionEnded(true); // Hiển thị modal kết thúc
        }
      });
    }
  };

  // Hàm chuyển đổi Float32 PCM sang Int16 PCM
  const float32ToInt16 = float32Array => {
    const int16Array = new Int16Array(float32Array.length);
    for (let i = 0; i < float32Array.length; i++) {
      const s = Math.max(-1, Math.min(1, float32Array[i]));
      int16Array[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
    }
    return int16Array;
  };

  // Chỉnh sửa hàm setupMediaDevices để sử dụng AudioWorklet thay vì MediaRecorder
  const setupMediaDevices = async () => {
    console.log(
      '[setupMediaDevices] ENTERING setupMediaDevices. Current recording state (state):',
      recording,
      'recordingRef:',
      recordingRef.current,
      'Paused:',
      paused,
      'SessionStarted:',
      sessionStarted,
    );
    try {
      cleanupMediaResources(); // Dọn dẹp tài nguyên media cũ

      let audioStream = null;
      let videoStream = null;

      const audioConstraints = selectedMic
        ? { deviceId: { exact: selectedMic }, ...AUDIO_CONFIG }
        : AUDIO_CONFIG;

      const videoConstraints = cameraEnabled
        ? (selectedCamera ? { deviceId: { exact: selectedCamera } } : true)
        : false;

      // Lấy audio stream (bắt buộc phải có)
      try {
        audioStream = await navigator.mediaDevices.getUserMedia({ audio: audioConstraints });
        console.log('[setupMediaDevices] Got audio stream');
      } catch (err) {
        console.error('[setupMediaDevices] Không thể truy cập micro:', err);
        setMediaError('Không thể truy cập micro: ' + err.message);
        return false;
      }

      // Lấy video stream nếu được yêu cầu
      if (videoConstraints) {
        try {
          videoStream = await navigator.mediaDevices.getUserMedia({ video: videoConstraints });
          console.log('[setupMediaDevices] Got video stream');
        } catch (err) {
          console.warn('[setupMediaDevices] Không thể truy cập camera (sẽ bỏ qua):', err);
          // Không báo lỗi để tiếp tục với audio
        }
      }

      // Gộp audio + video tracks lại thành 1 stream
      const stream = new MediaStream([
        ...audioStream.getAudioTracks(),
        ...(videoStream?.getVideoTracks() || []),
      ]);
      currentStreamRef.current = stream;

      console.log('[setupMediaDevices] Stream tracks:', stream.getTracks());

      if (videoRef.current) {
        if (videoStream) {
          videoRef.current.srcObject = videoStream;
          videoRef.current.onloadedmetadata = () => {
            videoRef.current.play().catch(error => {
              console.error('Không thể phát video:', error);
              setMediaError('Không thể phát video: ' + error.message);
            });
          };
        } else {
          // Nếu không có camera hoặc không bật, clear video
          videoRef.current.srcObject = null;
        }
      }

      // Khởi tạo AudioContext cho việc thu âm nếu chưa có
      if (!captureAudioContextRef.current) {
        captureAudioContextRef.current = new AudioContext({ sampleRate: AUDIO_CONFIG.sampleRate });
        console.log(`[setupMediaDevices] Capture AudioContext created with sampleRate ${AUDIO_CONFIG.sampleRate}Hz`);
      }

      // Resume AudioContext nếu nó bị suspend (thường do chính sách auto-play của trình duyệt)
      if (captureAudioContextRef.current.state === 'suspended') {
        await captureAudioContextRef.current.resume();
        console.log('[setupMediaDevices] Capture AudioContext resumed');
      }

      // Nạp AudioWorklet Processor cho việc thu âm
      if (!audioCaptureWorkletReadyRef.current) {
        const captureBlob = new Blob([audioCaptureProcessorCode], { type: 'application/javascript' });
        const captureProcessorUrl = URL.createObjectURL(captureBlob);
        try {
          await captureAudioContextRef.current.audioWorklet.addModule(captureProcessorUrl);
          audioCaptureWorkletReadyRef.current = true;
          console.log('[setupMediaDevices] AudioCaptureProcessor module added');
        } catch (error) {
          console.error('[setupMediaDevices] Error adding AudioCaptureProcessor module:', error);
          setMediaError(`Lỗi AudioWorklet: ${error.message}`);
          URL.revokeObjectURL(captureProcessorUrl);
          return false;
        }
        URL.revokeObjectURL(captureProcessorUrl);
      }

      // Tạo MediaStreamAudioSourceNode từ stream
      mediaStreamSourceRef.current = captureAudioContextRef.current.createMediaStreamSource(stream);

      // Tạo AudioWorkletNode
      if (!captureAudioWorkletNodeRef.current && audioCaptureWorkletReadyRef.current) {
        captureAudioWorkletNodeRef.current = new AudioWorkletNode(
          captureAudioContextRef.current,
          'audio-capture-processor',
        );
        console.log('[setupMediaDevices] AudioCaptureProcessor node created');

        captureAudioWorkletNodeRef.current.port.onmessage = event => {
          // console.log(
          //   '[setupMediaDevices] Received message from AudioCaptureProcessor node. event.data.type:',
          //   event.data.type,
          //   '!paused:',
          //   !paused,
          //   'sessionStarted:',
          //   sessionStarted,
          //   'RECORDING_REF_IN_ONMESSAGE:',
          //   recordingRef.current,
          //   'socketConnected:',
          //   socketRef.current?.connected,
          //   'sessionId:',
          //   sessionId,
          // );
          if (event.data.type === 'audioData') {
            const pcmFloat32Data = new Float32Array(event.data.pcmData);
            // Update state for visualizer
            if (!paused && sessionStarted && recordingRef.current) {
              setCurrentAudioFrameForVisualizer(pcmFloat32Data);
            } else {
              setCurrentAudioFrameForVisualizer(null); // Clear visualizer when not recording/paused
            }

            if (
              !paused &&
              sessionStarted &&
              recordingRef.current &&
              socketRef.current &&
              socketRef.current.connected &&
              sessionId
            ) {
              const pcmInt16Data = float32ToInt16(pcmFloat32Data);
              // console.log(
              //   '[setupMediaDevices] Processing audioData. Float32 length:',
              //   pcmFloat32Data.length,
              //   'Int16 bytes:',
              //   pcmInt16Data.buffer.byteLength,
              // );

              socketRef.current.emit('client:student_speech_chunk', {
                sessionId,
                audioChunk: pcmInt16Data.buffer, // Gửi ArrayBuffer của Int16Array
                format: {
                  encoding: 'PCM',
                  sampleRate: AUDIO_CONFIG.sampleRate,
                  bitsPerSample: 16,
                  channels: AUDIO_CONFIG.channelCount,
                  endian: 'little',
                },
              });
            } else if (event.data.type === 'audioData') {
              // console.log(
              //   '[setupMediaDevices] Received audioData, BUT NOT SENDING. Conditions: !paused:',
              //   !paused,
              //   'sessionStarted:',
              //   sessionStarted,
              //   'recordingRef.current:',
              //   recordingRef.current,
              //   'socketConnected:',
              //   socketRef.current?.connected,
              //   'sessionId:',
              //   sessionId,
              // );
            }
          }
        };
      } else if (!audioCaptureWorkletReadyRef.current) {
        console.error('[setupMediaDevices] AudioCaptureProcessor not ready, cannot create node.');
        setMediaError('Không thể khởi tạo bộ xử lý âm thanh.');
        return false;
      }

      // Kết nối source -> worklet (không kết nối worklet đến destination nếu chỉ thu âm)
      if (mediaStreamSourceRef.current && captureAudioWorkletNodeRef.current) {
        mediaStreamSourceRef.current.connect(captureAudioWorkletNodeRef.current);
        // Không cần dòng này nếu chỉ thu âm: captureAudioWorkletNodeRef.current.connect(captureAudioContextRef.current.destination);
        console.log('[setupMediaDevices] MediaStreamSource connected to AudioCaptureProcessor node');
      } else {
        console.error('[setupMediaDevices] Failed to connect audio graph for capture.');
        setMediaError('Lỗi kết nối pipeline âm thanh.');
        return false;
      }

      setMediaError(null);

      // Nếu session đã active và media sẵn sàng, bắt đầu ghi âm
      if (sessionStarted && !paused) {
        console.log(
          '[setupMediaDevices] Media ready and session active, calling startRecording(). Current recordingRef before call:',
          recordingRef.current,
        );
        startRecording();
      } else {
        console.log(
          '[setupMediaDevices] Media ready BUT session NOT active or IS paused. NOT calling startRecording(). sessionStarted:',
          sessionStarted,
          'paused:',
          paused,
        );
      }
      console.log(
        '[setupMediaDevices] EXITING setupMediaDevices. recordingRef after potential startRecording call:',
        recordingRef.current,
      );
      return true;
    } catch (error) {
      setMediaError(error.message);
      console.error('[setupMediaDevices] Error accessing media devices or setting up AudioWorklet:', error);
      toast.error(t('MEDIA_ACCESS_ERROR_WORKLET', 'Không thể truy cập camera/microphone hoặc lỗi AudioWorklet.'));
      console.log(
        '[setupMediaDevices] EXITING setupMediaDevices due to ERROR. recordingRef state:',
        recordingRef.current,
      );
      return false;
    }
  };

  // Bắt đầu thu âm - với AudioWorklet, việc "ghi âm" là cho phép gửi dữ liệu từ worklet
  const startRecording = () => {
    console.log(
      '[RolePlaySessionScreen] Attempting to START recording. Current recording state (state):',
      recording,
      'recordingRef:',
      recordingRef.current,
      'Paused:',
      paused,
      'SessionStarted:',
      sessionStarted,
    );
    // Đảm bảo AudioContext đang chạy (quan trọng nếu nó bị suspend)
    if (captureAudioContextRef.current && captureAudioContextRef.current.state === 'suspended') {
      captureAudioContextRef.current
        .resume()
        .then(() => {
          console.log('[startRecording] Capture AudioContext resumed.');
          setRecording(true);
          recordingRef.current = true;
          console.log('[startRecording] SET recording to TRUE (after resume). New recordingRef:', recordingRef.current);
        })
        .catch(err => console.error('[startRecording] Error resuming capture AudioContext:', err));
    } else {
      setRecording(true);
      recordingRef.current = true;
      console.log('[startRecording] SET recording to TRUE. New recordingRef:', recordingRef.current);
    }
    console.log('[startRecording] Recording flag and ref hopefully set to true. Worklet should send data.');
  };

  // Dừng thu âm
  const stopRecording = () => {
    console.log(
      '[RolePlaySessionScreen] Attempting to STOP recording. Current recording state (state):',
      recording,
      'recordingRef:',
      recordingRef.current,
    );
    setRecording(false);
    recordingRef.current = false;
    console.log('[stopRecording] SET recording to FALSE. New recordingRef:', recordingRef.current);
    console.log(
      '[stopRecording] Recording flag and ref hopefully set to false. Worklet should stop sending data if it checks ref.',
    );
    // Không cần dừng AudioWorkletNode trực tiếp, nó sẽ dừng xử lý khi không có input hoặc tự giải phóng
    // Nếu muốn dừng hẳn, có thể gửi message tới worklet hoặc disconnect node:
    // if (captureAudioWorkletNodeRef.current) {
    //   captureAudioWorkletNodeRef.current.port.postMessage('stop');
    // }
  };

  // Tạm dừng/tiếp tục phiên
  const togglePause = () => {
    if (socketRef.current && socketRef.current.connected) {
      setPaused(prev => !prev);
      socketRef.current.emit('pause', { paused: !paused });
    }
  };

  // Thêm hàm toggleMic để bật/tắt mic
  const toggleMic = () => {
    const newMicEnabledState = !micEnabled;
    setMicEnabled(newMicEnabledState);

    if (currentStreamRef.current && currentStreamRef.current.getAudioTracks().length > 0) {
      currentStreamRef.current.getAudioTracks().forEach(track => {
        track.enabled = newMicEnabledState;
      });
    }

    if (socketRef.current && socketRef.current.connected) {
      socketRef.current.emit('toggleMic', { micEnabled: !micEnabled });
    }
  };

  // Kết thúc phiên role play
  const endSession = async () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // Dừng ghi âm nếu đang ghi
    stopRecording();
    // Dừng phát âm thanh của AI ngay lập tức
    stopAiImmediately();

    // Cleanup media
    cleanupMediaResources();

    setSessionEnded(true); // Hiển thị modal kết thúc trước

    // Gửi sự kiện kết thúc session lên server
    if (socketRef.current && socketRef.current.connected && sessionId) {
      socketRef.current.emit('client:end_session', { sessionId });
      console.log('Emitted client:end_session');
    } else {
      console.warn('Socket not connected or sessionId missing, cannot emit end_session');
    }
    // Không ngắt kết nối socket ở đây ngay, chờ server xác nhận hoặc xử lý ở unmount
  };

  // Chuyển đến trang kết quả
  const goToResults = () => {
    console.log('Navigate to results page for session (mock): ', sessionId);
    setSessionEnded(false);
    navigate(LINK.ROLE_PLAY_SESSION_RESULT.format(courseId, sessionId));
  };

  // Hàm tổng hợp để cleanup media resources
  const cleanupMediaResources = () => {
    console.log('Cleaning up media resources');
    if (recording) {
      stopRecording();
      console.log("stop recording!")
    }

    // Disconnect và reset AudioWorklet capture nodes
    if (mediaStreamSourceRef.current && captureAudioWorkletNodeRef.current) {
      try {
        mediaStreamSourceRef.current.disconnect(captureAudioWorkletNodeRef.current);
        console.log('[cleanupMediaResources] Disconnected mediaStreamSource from captureAudioWorkletNode');
      } catch (e) {
        console.error('[cleanupMediaResources] Error disconnecting mediaStreamSource:', e);
      }
    }
    if (captureAudioWorkletNodeRef.current) {
      try {
        // Không cần disconnect worklet node khỏi destination nếu nó không được kết nối
        // captureAudioWorkletNodeRef.current.disconnect();
        console.log('[cleanupMediaResources] Capture AudioWorkletNode resources will be garbage collected.');
      } catch (e) {
        console.error('[cleanupMediaResources] Error disconnecting captureAudioWorkletNode:', e);
      }
      captureAudioWorkletNodeRef.current = null;
    }
    mediaStreamSourceRef.current = null;

    if (captureAudioContextRef.current) {
      captureAudioContextRef.current
        .close()
        .then(() => {
          console.log('[cleanupMediaResources] Capture AudioContext closed');
        })
        .catch(err => {
          console.error('[cleanupMediaResources] Error closing capture AudioContext:', err);
        });
      captureAudioContextRef.current = null;
      audioCaptureWorkletReadyRef.current = false;
    }

    // Dừng tất cả tracks trong stream
    if (currentStreamRef.current) {
      try {
        currentStreamRef.current.getTracks().forEach(track => {
          track.stop();
          console.log(`Stopped track: ${track.kind} - ${track.id}`);
        });
      } catch (e) {
        console.error('Error stopping stream tracks', e);
      }
    }

    // Dừng stream từ video element
    if (videoRef.current && videoRef.current.srcObject) {
      try {
        const stream = videoRef.current.srcObject;
        stream.getTracks().forEach(track => {
          track.stop();
          console.log(`Stopped video track: ${track.kind} - ${track.id}`);
        });
        videoRef.current.srcObject = null;
      } catch (e) {
        console.error('Error stopping video stream', e);
      }
    }

    // Reset các biến tham chiếu
    // mediaRecorderRef.current = null; // Loại bỏ MediaRecorder
    currentStreamRef.current = null;
  };

  // Fetch completed role play sessions for the current student and course
  useEffect(() => {
    const fetchAllSessions = async () => {
      if (!courseId || !user || !user._id) {
        setCompletedSessions([]);
        setLoadingCompletedSessions(false);
        console.warn('Cannot fetch completed sessions: courseId or user details missing.');
        return;
      }

      setLoadingCompletedSessions(true);
      console.log(`Fetching completed sessions for course ${courseId}, student ${user._id}`);

      try {
        const allSessions = [];
        let currentPage = 1;
        const pageSize = 100;
        let hasMore = true;

        while (hasMore) {
          const sessionsData = await getRolePlaySessionsByStudent(courseId, { page: currentPage, pageSize }, false);

          if (sessionsData?.rows?.length) {
            allSessions.push(...sessionsData.rows);
          }

          const total = sessionsData?.total || 0;
          const fetchedSoFar = currentPage * pageSize;
          hasMore = fetchedSoFar < total;
          currentPage++;
        }

        setCompletedSessions(allSessions);
      } catch (error) {
        console.error('Error fetching completed sessions:', error);
        toast.error(t('ERROR_FETCHING_COMPLETED_SESSIONS', 'Lỗi khi tải danh sách phiên đã hoàn thành.'));
        setCompletedSessions([]);
      } finally {
        setLoadingCompletedSessions(false);
      }
    };

    fetchAllSessions();
  }, [courseId, user, t, sessionEnded]);

  useEffect(() => {
    fetchCourseAndTaskData();
    fetchGetByCourse();

    // Khởi tạo AudioWorklet
    initAudioWorklet().catch(err => {
      console.error('Không thể khởi tạo AudioWorklet:', err);
      setAudioWorkletError(err.message);
    });

    // Cleanup
    return () => {
      console.log('Component unmounting, cleaning up resources');
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Đóng WebSocket connection khi component unmount
      if (socketRef.current) {
        console.log('Disconnecting WebSocket on unmount');
        socketRef.current.disconnect();
        socketRef.current = null;
      }

      cleanupMediaResources();
      cleanupAudioWorklet(); // Đây là cleanup cho AudioWorklet playback
    };
  }, [courseId]);

  useEffect(() => {
    // Check for setSinkId support once on component mount
    if (typeof window.AudioContext !== 'undefined' && 'setSinkId' in window.AudioContext.prototype) {
      setIsSpeakerSelectionSupported(true);
    }
  }, [courseId]);

  // Gọi setupMediaDevices khi videoRef đã mount và session bắt đầu VÀ socket đã kết nối
  useEffect(() => {
    console.log(
      '[useEffect for setupMediaDevices] TRIGGERED. Dependencies changed. showIntro:',
      showIntroduction,
      'sessionStarted:',
      sessionStarted,
      'socketStatus:',
      socketStatus,
      'selectedMic:',
      selectedMic,
      'selectedCamera:',
      selectedCamera,
      'cameraEnabled:',
      cameraEnabled,
    );
    if (!showIntroduction && sessionStarted && videoRef.current && socketStatus === SOCKET_STATUS.CONNECTED) {
      console.log('[useEffect for setupMediaDevices] CONDITIONS MET. Calling setupMediaDevices.');
      setupMediaDevices();
    } else {
      console.log(
        '[useEffect for setupMediaDevices] CONDITIONS NOT MET. Not calling setupMediaDevices. Details - showIntro:',
        showIntroduction,
        'sessionStarted:',
        sessionStarted,
        'videoRef.current:',
        !!videoRef.current,
        'socketStatus:',
        socketStatus,
      );
    }
    // eslint-disable-next-line
  }, [showIntroduction, sessionStarted, videoRef.current, socketStatus, selectedMic, selectedCamera, cameraEnabled]);

  // Format time to MM:SS
  const formatTime = seconds => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Tính toán progress
  // const totalDurationSeconds = (task?.estimated_duration || 0) * 60; // Needs to be updated for course
  const totalDurationSeconds = scenariosDetail?.estimatedCallTimeInMinutes
    ? scenariosDetail.estimatedCallTimeInMinutes * 60
    : tasksList.reduce((acc, currTask) => acc + (currTask.estimated_duration || 0), 0) * 60;
  const progressPercent = totalDurationSeconds > 0 ? Math.min(100, (elapsedTime / totalDurationSeconds) * 100) : 0;

  // Thêm kiểm tra socket status để disable nút bắt đầu nếu đang kết nối hoặc lỗi
  const isStartButtonDisabled =
    loading ||
    !course ||
    // !persona || // Changed from !task to !course
    socketStatus === SOCKET_STATUS.CONNECTING ||
    socketStatus === SOCKET_STATUS.ERROR;

  // Kiểm tra nếu không có course hoặc đang tải dữ liệu chính
  if (loading && !course) {
    // Initial loading for the whole page data
    return <Loading />;
  }

  // Nếu tải xong mà không có course (ví dụ lỗi)
  if (!loading && !course) {
    return (
      <div className="role-play-session-screen">
        <p>{t('ERROR_LOADING_SESSION_DATA', 'Không thể tải dữ liệu cho khóa học này.')}</p>
      </div>
    );
  }

  // Render với các component mới
  return (
    <div className="role-play-session-screen">
      <div className="role-play-session-screen__content-container">
        {loading && !course ? ( // Initial loading for the whole page data
          <Loading />
        ) : !course ? (
          <div className="role-play-session-screen">
            <p>{t('ERROR_LOADING_SESSION_DATA', 'Không thể tải dữ liệu cho khóa học này.')}</p>
          </div>
        ) : showIntroduction ? (
          <div className="session-introduction-layout">
            <SessionIntroductionComponent
              course={course}
              scenarios={scenarios}
              sessions={completedSessions} // Thêm sessions để tính toán progress
              // task={task} // Removed
              tasksList={tasksList} // Pass tasksList for display if needed
              persona={persona}
              onStartSession={startSession}
              // onBackToTaskList={backToTaskList} // Changed to handleLeaveSession
              onBackToCourseList={() => {
                handleLeaveSession();
                navigate('/student/courses')
              }} // New prop or rename existing
              isStartButtonDisabled={isStartButtonDisabled}
              setPersona={setPersona}
              setScenariosDetail={setScenariosDetail}
              socketStatus={socketStatus}
              SOCKET_STATUS={SOCKET_STATUS}
            />
            <CompletedSessionsListComponent
              scenarios={scenarios}
              sessions={completedSessions}
              loading={loadingCompletedSessions}
              courseId={courseId}
            />
          </div>
        ) : (
          <NewSessionMainScreen
            course={course}
            // task={task} // Removed
            scenariosDetail={scenariosDetail}
            tasksList={tasksList} // Pass tasksList for display if needed
            persona={persona}
            videoRef={videoRef}
            transcript={transcript}
            elapsedTime={elapsedTime}
            totalDurationSeconds={totalDurationSeconds}
            progressPercent={progressPercent}
            paused={paused}
            sessionEnded={sessionEnded}
            socketStatus={socketStatus}
            sessionStarted={sessionStarted}
            isAiSpeaking={isAiSpeaking}
            isAiProcessing={isAiProcessing}
            isAiInterrupted={isAiInterrupted}
            currentAudioFrameForVisualizer={currentAudioFrameForVisualizer}
            aiAudioFrameForVisualizer={aiAudioFrameForVisualizer} // Pass AI visualizer data
            onTogglePause={togglePause}
            onEndSession={endSession}
            // onBackToTaskList={backToTaskList} // Changed to handleLeaveSession
            onBackToCourseList={handleLeaveSession} // New prop or rename existing
            SOCKET_STATUS={SOCKET_STATUS}
            mediaError={mediaError}
            onToggleMic={toggleMic}
            micEnabled={micEnabled}
          />
        )}

        <MediaDeviceSelectionModal
          open={showMediaDeviceModal}
          onCancel={() => {
            setShowMediaDeviceModal(false);
            setShowIntroduction(true);
          }}
          onOk={startSessionWithSelectedDevices}
          onRefreshDevices={enumerateDevices}
          enumeratingDevices={enumeratingDevices}
          cameraEnabled={cameraEnabled}
          onCameraEnabledChange={setCameraEnabled}
          selectedCamera={selectedCamera}
          onCameraChange={setSelectedCamera}
          selectedMic={selectedMic}
          onMicChange={setSelectedMic}
          availableCameras={availableCameras}
          availableMics={availableMics}
          availableSpeakers={availableSpeakers}
          selectedSpeaker={selectedSpeaker}
          onSpeakerChange={setSelectedSpeaker}
          isSpeakerSelectionSupported={isSpeakerSelectionSupported}
        />

        <SessionEndModal
          open={sessionEnded}
          onCancel={() => setSessionEnded(false)}
          onBackToTasks={
            () => {
              setSessionEnded(false);
              // setShowIntroduction(true);
              handleLeaveSession();
            }
          } // Changed to handleLeaveSession
          onBackToCourseList={() => {
            setSessionEnded(false);
            handleLeaveSession();
          }} // New prop or rename existing
          onViewResults={goToResults}
        />
      </div>
    </div>
  );
};

export default RolePlaySessionScreen;
