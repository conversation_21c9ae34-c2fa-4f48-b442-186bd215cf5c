# Test Modal Functionality

## Kiểm tra chức năng Modal

### 1. <PERSON><PERSON><PERSON> tra nút "Thêm chủ đề / nhiệm vụ" trong AIScenarioTabs

**Vị trí:** Trong phần Tasks Management của mỗi scenario tab
**Hành động:** Click nút "Thêm chủ đề / nhiệm vụ"
**Kết quả mong đợi:** Modal TaskFormModal sẽ mở với form trống

### 2. Kiểm tra nút "Thêm chủ đề / nhiệm vụ" trong TaskEvaluationDisplayCard

**Vị trí:** Trong phần buttons của TaskEvaluationDisplayCard (khi hideButtons=false)
**Hành động:** Click nút "Thêm chủ đề / nhiệm vụ"
**Kết quả mong đợi:** Modal TaskFormModal sẽ mở với form trống

### 3. <PERSON>ểm tra nút "Chỉnh sửa" trong bảng

**Vị trí:** Cột Actions của mỗi row trong bảng tasks
**Hành động:** Click nút "Chỉnh sửa" (icon EditOutlined)
**Kết quả mong đợi:** Modal TaskFormModal sẽ mở với dữ liệu của task đã được điền sẵn

## Các thay đổi đã thực hiện

### 1. AIScenarioTabs.js
- Cập nhật nút "Thêm chủ đề / nhiệm vụ" để gọi `taskCardRef.current.showTaskModal()`
- Loại bỏ logic tạo task trực tiếp trong onClick handler

### 2. TaskEvaluationDisplayCard.js
- Thêm method `showTaskModal` vào useImperativeHandle
- Method này sẽ reset editingTask và mở modal

### 3. TaskFormModal.js
- Modal đã được tạo với đầy đủ chức năng
- Hỗ trợ cả thêm mới và chỉnh sửa
- Validation đầy đủ

## Flow hoạt động

### Thêm mới task:
1. User click nút "Thêm chủ đề / nhiệm vụ"
2. AIScenarioTabs gọi `taskCardRef.current.showTaskModal()`
3. TaskEvaluationDisplayCard set `editingTask = null` và `isTaskModalVisible = true`
4. Modal mở với form trống
5. User điền thông tin và submit
6. `handleTaskSubmit` được gọi với `editingTask = null` (chế độ thêm mới)
7. Tạo task mới với temp ID và gọi `onTaskAdd`
8. AIScenarioTabs nhận callback và cập nhật scenario

### Chỉnh sửa task:
1. User click nút "Chỉnh sửa" trong bảng
2. `handleEditTask` được gọi với task data
3. Set `editingTask = task` và `isTaskModalVisible = true`
4. Modal mở với form đã điền sẵn dữ liệu
5. User sửa đổi và submit
6. `handleTaskSubmit` được gọi với `editingTask != null` (chế độ chỉnh sửa)
7. Merge dữ liệu mới với task cũ và gọi `onTaskUpdate`
8. AIScenarioTabs nhận callback và cập nhật scenario

## Debugging

Nếu modal không mở, kiểm tra:

1. **Console errors:** Mở Developer Tools và kiểm tra console
2. **Ref connection:** Đảm bảo `taskCardRef` được truyền đúng
3. **Method existence:** Kiểm tra `taskCardRef.current.showTaskModal` có tồn tại không
4. **State updates:** Kiểm tra `isTaskModalVisible` có được set thành true không

## Test Cases

### Test Case 1: Thêm task mới từ AIScenarioTabs
```
1. Vào trang course detail
2. Chọn một scenario tab
3. Click "Thêm chủ đề / nhiệm vụ" trong phần Tasks Management
4. Verify: Modal mở với title "Thêm nhiệm vụ mới"
5. Điền form và submit
6. Verify: Task mới xuất hiện trong bảng
```

### Test Case 2: Chỉnh sửa task
```
1. Có ít nhất 1 task trong bảng
2. Click nút "Chỉnh sửa" trong cột Actions
3. Verify: Modal mở với title "Chỉnh sửa nhiệm vụ"
4. Verify: Form đã điền sẵn dữ liệu
5. Sửa đổi một số trường và submit
6. Verify: Dữ liệu trong bảng được cập nhật
```

### Test Case 3: Validation
```
1. Mở modal thêm mới
2. Để trống trường "Chủ đề nhiệm vụ" và submit
3. Verify: Hiển thị lỗi validation
4. Để trống trường "Hướng dẫn đánh giá" và submit
5. Verify: Hiển thị lỗi validation
```

### Test Case 4: Cancel modal
```
1. Mở modal
2. Điền một số thông tin
3. Click "Hủy"
4. Verify: Modal đóng và không lưu dữ liệu
5. Mở lại modal
6. Verify: Form đã được reset
```
