import React, {useState, useEffect} from 'react';
import {Modal, Form, Input, InputNumber, Switch, Space, Button} from 'antd';
import {useTranslation} from 'react-i18next';
import {PlusOutlined, DeleteOutlined} from '@ant-design/icons';

import AntButton from '@component/AntButton';
import {BUTTON} from '@constant';

const {TextArea} = Input;

const TaskFormModal = ({
  visible,
  onCancel,
  onSubmit,
  initialData = null,
  loading = false,
}) => {
  const {t} = useTranslation();
  const [form] = Form.useForm();
  const [helpfulLinks, setHelpfulLinks] = useState([]);

  const isEditMode = !!initialData;

  useEffect(() => {
    if (visible) {
      if (isEditMode && initialData) {
        // Chế độ chỉnh sửa - điền dữ liệu có sẵn
        form.setFieldsValue({
          name: initialData.name || '',
          description: initialData.description || '',
          evaluationGuidelines: initialData.evaluationGuidelines || '',
          weight: initialData.weight || 0,
          exampleVideoUrl: initialData.exampleVideoUrl || '',
          isMakeOrBreak: initialData.isMakeOrBreak || false,
        });
        setHelpfulLinks(initialData.helpfulLinks || []);
      } else {
        // Chế độ thêm mới - reset form
        form.resetFields();
        setHelpfulLinks([]);
      }
    }
  }, [visible, isEditMode, initialData, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const taskData = {
        ...values,
        helpfulLinks: helpfulLinks.filter(link => link.trim() !== ''),
      };

      if (isEditMode) {
        taskData._id = initialData._id;
      }

      onSubmit(taskData);
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setHelpfulLinks([]);
    onCancel();
  };

  const addHelpfulLink = () => {
    setHelpfulLinks([...helpfulLinks, '']);
  };

  const updateHelpfulLink = (index, value) => {
    const newLinks = [...helpfulLinks];
    newLinks[index] = value;
    setHelpfulLinks(newLinks);
  };

  const removeHelpfulLink = (index) => {
    const newLinks = helpfulLinks.filter((_, i) => i !== index);
    setHelpfulLinks(newLinks);
  };

  return (
    <Modal
      title={isEditMode ? t('EDIT_TASK', 'Chỉnh sửa nhiệm vụ') : t('ADD_TASK', 'Thêm nhiệm vụ mới')}
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          {t('CANCEL', 'Hủy')}
        </Button>,
        <AntButton
          key="submit"
          type={BUTTON.DEEP_NAVY}
          loading={loading}
          onClick={handleSubmit}
        >
          {isEditMode ? t('UPDATE', 'Cập nhật') : t('CREATE', 'Tạo mới')}
        </AntButton>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          weight: 0,
          isMakeOrBreak: false,
        }}
      >
        <Form.Item
          name="name"
          label={t('TASK_TOPIC', 'Chủ đề nhiệm vụ')}
          rules={[
            {required: true, message: t('TASK_TOPIC_REQUIRED', 'Vui lòng nhập chủ đề nhiệm vụ')},
            {max: 255, message: t('TASK_TOPIC_TOO_LONG', 'Chủ đề nhiệm vụ không được vượt quá 255 ký tự')},
          ]}
        >
          <TextArea
            placeholder={t('ENTER_TASK_TOPIC', 'Nhập chủ đề nhiệm vụ')}
            autoSize={{minRows: 2, maxRows: 4}}
          />
        </Form.Item>

        <Form.Item
          name="description"
          label={t('TASK_DESCRIPTION', 'Mô tả nhiệm vụ')}
          rules={[
            {max: 1000, message: t('TASK_DESCRIPTION_TOO_LONG', 'Mô tả nhiệm vụ không được vượt quá 1000 ký tự')},
          ]}
        >
          <TextArea
            placeholder={t('ENTER_TASK_DESCRIPTION', 'Nhập mô tả nhiệm vụ')}
            autoSize={{minRows: 3, maxRows: 6}}
          />
        </Form.Item>

        <Form.Item
          name="evaluationGuidelines"
          label={t('EVALUATION_GUIDELINES', 'Hướng dẫn đánh giá')}
          rules={[
            {required: true, message: t('EVALUATION_GUIDELINES_REQUIRED', 'Vui lòng nhập hướng dẫn đánh giá')},
            {max: 2000, message: t('EVALUATION_GUIDELINES_TOO_LONG', 'Hướng dẫn đánh giá không được vượt quá 2000 ký tự')},
          ]}
        >
          <TextArea
            placeholder={t('ENTER_EVALUATION_GUIDELINES', 'Nhập hướng dẫn đánh giá')}
            autoSize={{minRows: 4, maxRows: 8}}
          />
        </Form.Item>

        <Form.Item
          name="weight"
          label={t('TASK_WEIGHT', 'Trọng số')}
          rules={[
            {required: true, message: t('TASK_WEIGHT_REQUIRED', 'Vui lòng nhập trọng số')},
            {type: 'number', min: 0, max: 100, message: t('TASK_WEIGHT_RANGE', 'Trọng số phải từ 0 đến 100')},
          ]}
        >
          <InputNumber
            placeholder={t('ENTER_TASK_WEIGHT', 'Nhập trọng số')}
            min={0}
            max={100}
            style={{width: '100%'}}
          />
        </Form.Item>

        <Form.Item
          name="exampleVideoUrl"
          label={t('EXAMPLE_VIDEO_URL', 'URL video mẫu')}
          rules={[
            {type: 'url', message: t('INVALID_URL', 'URL không hợp lệ')},
          ]}
        >
          <Input placeholder={t('ENTER_EXAMPLE_VIDEO_URL', 'Nhập URL video mẫu')} />
        </Form.Item>

        <Form.Item
          name="isMakeOrBreak"
          label={t('IS_MAKE_OR_BREAK', 'Nhiệm vụ quyết định')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item label={t('HELPFUL_LINKS', 'Liên kết hữu ích')}>
          <Space direction="vertical" style={{width: '100%'}}>
            {helpfulLinks.map((link, index) => (
              <Space key={index} style={{width: '100%'}}>
                <Input
                  placeholder={t('ENTER_HELPFUL_LINK', 'Nhập liên kết hữu ích')}
                  value={link}
                  onChange={(e) => updateHelpfulLink(index, e.target.value)}
                  style={{flex: 1}}
                />
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => removeHelpfulLink(index)}
                />
              </Space>
            ))}
            <Button
              type="dashed"
              onClick={addHelpfulLink}
              icon={<PlusOutlined />}
              style={{width: '100%'}}
            >
              {t('ADD_HELPFUL_LINK', 'Thêm liên kết hữu ích')}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TaskFormModal;
