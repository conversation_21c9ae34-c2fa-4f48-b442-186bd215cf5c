.task-evaluation-display-card {
  .table-container {
    padding: 0;
    overflow-x: auto;
  }

  .evaluation-table {
    width: 100%;
    table-layout: fixed;

    .ant-table-thead > tr > th {
      background-color: #f8f9fa; // Light grey background for header
      font-weight: 600;
      color: #343a40; // Darker text for header
      padding: 12px 10px; // Adjust padding for header cells
      word-break: break-word;

      .anticon-question-circle {
        margin-left: 4px;
        color: #868e96; // Lighter color for question icons
      }
    }

    .ant-table-tbody > tr > td {
      padding: 10px; // Adjust padding for body cells
      vertical-align: top; // Align content to the top for multi-line text
      font-size: 13px;
      word-break: break-word;
    }

    .evaluation-guidelines-cell {
      display: flex;
      align-items: flex-start; // Align text and button to top
      justify-content: space-between;
      gap: 8px;

      .ant-typography {
        white-space: pre-line; // Preserve newlines from the string
        margin-bottom: 0;
        flex-grow: 1;
      }

      .sync-guidelines-btn {
        flex-shrink: 0;
        // Style for the sync button if needed
      }
    }

    .ant-input-number {
        font-size: 13px;
    }

    .ant-switch {
      // Custom styles for switch if needed
    }

    .task-row {
      &:hover {
        background-color: #fafafa;
      }
    }

    // Styles for action buttons
    .ant-space {
      display: flex;
      justify-content: center;

      .ant-btn {
        padding: 0 8px;
        height: 28px;

        &[disabled] {
          opacity: 0.5;
        }
      }
    }
  }
}
